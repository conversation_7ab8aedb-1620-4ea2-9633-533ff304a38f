using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class StatisticsService : IStatisticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StatisticsService> _logger;

        public StatisticsService(
            IUnitOfWork unitOfWork,
            ILogger<StatisticsService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<GeneralStatisticsDto>> GetGeneralStatisticsAsync()
        {
            try
            {
                var allOrders = await _unitOfWork.Orders.GetAllAsync();
                
                var totalRequests = allOrders.Count();
                var completedRequests = allOrders.Count(o => o.OrderStatus == OrderStatus.Accepted);
                var pendingRequests = allOrders.Count(o => 
                    o.OrderStatus == OrderStatus.DM || 
                    o.OrderStatus == OrderStatus.A1 || 
                    o.OrderStatus == OrderStatus.A2 || 
                    o.OrderStatus == OrderStatus.A3 || 
                    o.OrderStatus == OrderStatus.A4 || 
                    o.OrderStatus == OrderStatus.B || 
                    o.OrderStatus == OrderStatus.C || 
                    o.OrderStatus == OrderStatus.D);

                // Calculate average completion time
                var completedOrdersWithDates = allOrders.Where(o => 
                    o.OrderStatus == OrderStatus.Accepted && 
                    !string.IsNullOrEmpty(o.HumanResourcesManager))
                    .ToList();

                double avgTimeToComplete = 0;
                if (completedOrdersWithDates.Any())
                {
                    var totalDays = 0;
                    var validOrders = 0;

                    foreach (var order in completedOrdersWithDates)
                    {
                        var outDate = OrderHelper.ExtractOutDate(order.HumanResourcesManager);
                        if (DateTime.TryParse(outDate, out var completionDate))
                        {
                            var days = (completionDate - order.CreatedAt).TotalDays;
                            if (days >= 0)
                            {
                                totalDays += (int)days;
                                validOrders++;
                            }
                        }
                    }

                    if (validOrders > 0)
                    {
                        avgTimeToComplete = Math.Round((double)totalDays / validOrders, 1);
                    }
                }

                var statistics = new GeneralStatisticsDto
                {
                    TotalRequests = totalRequests,
                    CompletedRequests = completedRequests,
                    PendingRequests = pendingRequests,
                    AverageTimeToComplete = avgTimeToComplete
                };

                return ServiceResult<GeneralStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting general statistics");
                return ServiceResult<GeneralStatisticsDto>.Failure($"حدث خطأ أثناء جلب الإحصائيات العامة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<StageStatisticsDto>> GetStageStatisticsAsync()
        {
            try
            {
                var allOrders = await _unitOfWork.Orders.GetAllAsync();

                var stageStats = new StageStatisticsDto
                {
                    DepartmentManager = allOrders.Count(o => o.OrderStatus == OrderStatus.DM),
                    AssistantManagerA1 = allOrders.Count(o => o.OrderStatus == OrderStatus.A1),
                    AssistantManagerA2 = allOrders.Count(o => o.OrderStatus == OrderStatus.A2),
                    AssistantManagerA3 = allOrders.Count(o => o.OrderStatus == OrderStatus.A3),
                    AssistantManagerA4 = allOrders.Count(o => o.OrderStatus == OrderStatus.A4),
                    HRCoordinator = allOrders.Count(o => o.OrderStatus == OrderStatus.B),
                    Supervisors = allOrders.Count(o => o.OrderStatus == OrderStatus.C),
                    HRManager = allOrders.Count(o => o.OrderStatus == OrderStatus.D)
                };

                return ServiceResult<StageStatisticsDto>.Success(stageStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stage statistics");
                return ServiceResult<StageStatisticsDto>.Failure($"حدث خطأ أثناء جلب إحصائيات المراحل: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<OrderDepartmentStatisticsDto>>> GetDepartmentStatisticsAsync()
        {
            try
            {
                var allOrders = await _unitOfWork.Orders.GetAllAsync();

                var departmentStats = allOrders
                    .GroupBy(o => o.Department)
                    .Select(g => new OrderDepartmentStatisticsDto
                    {
                        DepartmentName = g.Key ?? "غير محدد",
                        TotalOrders = g.Count(),
                        CompletedOrders = g.Count(o => o.OrderStatus == OrderStatus.Accepted),
                        PendingOrders = g.Count(o => 
                            o.OrderStatus == OrderStatus.DM || 
                            o.OrderStatus == OrderStatus.A1 || 
                            o.OrderStatus == OrderStatus.A2 || 
                            o.OrderStatus == OrderStatus.A3 || 
                            o.OrderStatus == OrderStatus.A4 || 
                            o.OrderStatus == OrderStatus.B || 
                            o.OrderStatus == OrderStatus.C || 
                            o.OrderStatus == OrderStatus.D),
                        CancelledOrders = g.Count(o => 
                            o.OrderStatus == OrderStatus.CancelledByDepartmentManager ||
                            o.OrderStatus == OrderStatus.CancelledByAssistantManager ||
                            o.OrderStatus == OrderStatus.CancelledByCoordinator ||
                            o.OrderStatus == OrderStatus.CancelledByManager)
                    })
                    .OrderByDescending(d => d.TotalOrders)
                    .ToList();

                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Success(departmentStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting department statistics");
                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات الأقسام: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<SupervisorStatisticsDto>>> GetSupervisorStatisticsAsync()
        {
            try
            {
                var allOrders = await _unitOfWork.Orders.GetAllAsync();

                var supervisorStats = new List<SupervisorStatisticsDto>();

                // Define supervisor fields
                var supervisorFields = new[]
                {
                    "SupervisorOfEmployeeServices",
                    "SupervisorOfHumanResourcesPlanning", 
                    "SupervisorOfInformationTechnology",
                    "SupervisorOfAttendance",
                    "SupervisorOfMedicalRecords",
                    "SupervisorOfPayrollAndBenefits",
                    "SupervisorOfLegalAndCompliance",
                    "SupervisorOfHumanResourcesServices",
                    "SupervisorOfHousing",
                    "SupervisorOfFiles",
                    "SupervisorOfOutpatientClinics",
                    "SupervisorOfSocialSecurity",
                    "SupervisorOfInventoryControl",
                    "SupervisorOfRevenueDevelopment",
                    "SupervisorOfSecurity",
                    "SupervisorOfMedicalConsultation"
                };

                var supervisorNames = new[]
                {
                    "خدمات الموظفين",
                    "إدارة تخطيط الموارد البشرية",
                    "إدارة تقنية المعلومات",
                    "مراقبة الدوام",
                    "السجلات الطبية",
                    "إدارة الرواتب والاستحقاقات",
                    "إدارة القانونية والالتزام",
                    "خدمات الموارد البشرية",
                    "إدارة الإسكان",
                    "قسم الملفات",
                    "العيادات الخارجية",
                    "التأمينات الاجتماعية",
                    "وحدة مراقبة المخزون",
                    "إدارة تنمية الإيرادات",
                    "إدارة الأمن و السلامة",
                    "الطب الاتصالي"
                };

                for (int i = 0; i < supervisorFields.Length; i++)
                {
                    var fieldName = supervisorFields[i];
                    var supervisorName = supervisorNames[i];

                    var ordersWithSupervisor = allOrders.Where(o => 
                        !string.IsNullOrEmpty(GetSupervisorValue(o, fieldName))).ToList();

                    if (ordersWithSupervisor.Any())
                    {
                        var underExecution = ordersWithSupervisor.Count(o => 
                            GetSupervisorValue(o, fieldName).Contains("الطلب قيد التنفيذ"));
                        
                        var completed = ordersWithSupervisor.Count(o => 
                            GetSupervisorValue(o, fieldName).Contains("اعتماد"));
                        
                        var needsAction = ordersWithSupervisor.Count(o => 
                            GetSupervisorValue(o, fieldName).Contains("طلب إجراء"));
                        
                        var returned = ordersWithSupervisor.Count(o => 
                            GetSupervisorValue(o, fieldName).Contains("تمت الإعادة"));

                        // Calculate average completion time
                        double avgCompletionTime = 0;
                        var completedOrders = ordersWithSupervisor.Where(o => 
                            GetSupervisorValue(o, fieldName).Contains("اعتماد")).ToList();

                        if (completedOrders.Any())
                        {
                            var totalDays = 0;
                            var validOrders = 0;

                            foreach (var order in completedOrders)
                            {
                                var supervisorValue = GetSupervisorValue(order, fieldName);
                                var outDate = OrderHelper.ExtractOutDate(supervisorValue);
                                if (DateTime.TryParse(outDate, out var completionDate))
                                {
                                    var days = (completionDate - order.CreatedAt).TotalDays;
                                    if (days >= 0)
                                    {
                                        totalDays += (int)days;
                                        validOrders++;
                                    }
                                }
                            }

                            if (validOrders > 0)
                            {
                                avgCompletionTime = Math.Round((double)totalDays / validOrders, 1);
                            }
                        }

                        supervisorStats.Add(new SupervisorStatisticsDto
                        {
                            SupervisorName = supervisorName,
                            UnderExecution = underExecution,
                            Completed = completed,
                            NeedsAction = needsAction,
                            Returned = returned,
                            AverageCompletionTime = avgCompletionTime
                        });
                    }
                }

                return ServiceResult<List<SupervisorStatisticsDto>>.Success(supervisorStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor statistics");
                return ServiceResult<List<SupervisorStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات المشرفين: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetTransferTypeStatisticsAsync()
        {
            try
            {
                var allOrders = await _unitOfWork.Orders.GetAllAsync();

                var transferTypeStats = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.TransferType))
                    .GroupBy(o => o.TransferType)
                    .Select(g => new TransferTypeStatisticsDto
                    {
                        TransferType = g.Key,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Count)
                    .ToList();

                return ServiceResult<List<TransferTypeStatisticsDto>>.Success(transferTypeStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transfer type statistics");
                return ServiceResult<List<TransferTypeStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات نوع التحويل: {ex.Message}");
            }
        }

        private string GetSupervisorValue(Core.Models.OrdersTable order, string fieldName)
        {
            return fieldName switch
            {
                "SupervisorOfEmployeeServices" => order.SupervisorOfEmployeeServices,
                "SupervisorOfHumanResourcesPlanning" => order.SupervisorOfHumanResourcesPlanning,
                "SupervisorOfInformationTechnology" => order.SupervisorOfInformationTechnology,
                "SupervisorOfAttendance" => order.SupervisorOfAttendance,
                "SupervisorOfMedicalRecords" => order.SupervisorOfMedicalRecords,
                "SupervisorOfPayrollAndBenefits" => order.SupervisorOfPayrollAndBenefits,
                "SupervisorOfLegalAndCompliance" => order.SupervisorOfLegalAndCompliance,
                "SupervisorOfHumanResourcesServices" => order.SupervisorOfHumanResourcesServices,
                "SupervisorOfHousing" => order.SupervisorOfHousing,
                "SupervisorOfFiles" => order.SupervisorOfFiles,
                "SupervisorOfOutpatientClinics" => order.SupervisorOfOutpatientClinics,
                "SupervisorOfSocialSecurity" => order.SupervisorOfSocialSecurity,
                "SupervisorOfInventoryControl" => order.SupervisorOfInventoryControl,
                "SupervisorOfRevenueDevelopment" => order.SupervisorOfRevenueDevelopment,
                "SupervisorOfSecurity" => order.SupervisorOfSecurity,
                "SupervisorOfMedicalConsultation" => order.SupervisorOfMedicalConsultation,
                _ => string.Empty
            };
        }
    }
} 