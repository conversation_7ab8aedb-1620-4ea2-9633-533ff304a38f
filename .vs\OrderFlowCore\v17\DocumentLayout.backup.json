{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\ordermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\ordermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\interfaces\\repositories\\iunitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\interfaces\\repositories\\iunitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\data\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\data\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\ordercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\ordercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\fileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\fileservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\directmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\directmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 199, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "UnitOfWork.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs*", "RelativeToolTip": "OrderFlowCore.Infrastructure\\Data\\UnitOfWork.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGYAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:52:52.21Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "IUnitOfWork.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Repositories\\IUnitOfWork.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Interfaces\\Repositories\\IUnitOfWork.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Repositories\\IUnitOfWork.cs*", "RelativeToolTip": "OrderFlowCore.Application\\Interfaces\\Repositories\\IUnitOfWork.cs*", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:51:33.681Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OrderManagementService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderManagementService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\OrderManagementService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderManagementService.cs*", "RelativeToolTip": "OrderFlowCore.Application\\Services\\OrderManagementService.cs*", "ViewState": "AgIAACgAAAAAAAAAAAAAAOYAAABsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:33:29.732Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OrderController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\OrderController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\OrderController.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAwwEkAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:33:13.902Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "FileService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\FileService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\FileService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\FileService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\FileService.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAqwJQAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:26:28.688Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DirectManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\DirectManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\DirectManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\DirectManagerController.cs*", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\DirectManagerController.cs*", "ViewState": "AgIAAGQAAAAAAAAAAAAcwGsAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T10:25:06.924Z", "EditorCaption": ""}]}]}]}