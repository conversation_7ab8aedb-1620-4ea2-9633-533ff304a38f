using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using OrderFlowCore.Core.Entities;
using System;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Web.Extentions;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Coordinator)]
    public class HRCoordinatorController : Controller
    {
        private readonly IHRCoordinatorOrderService _hrCoordinatorOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly IOrderRestorationService _orderRestorationService;
        private readonly IDashboardService _dashboardService;
        private readonly IPathManagementService _pathManagementService;
        private readonly ILogger<HRCoordinatorController> _logger;

        public HRCoordinatorController(
            IHRCoordinatorOrderService hrCoordinatorOrderService,
            IOrderManagementService orderManagementService,
            IOrderRestorationService orderRestorationService,
            IDashboardService dashboardService,
            IPathManagementService pathManagementService,
            ILogger<HRCoordinatorController> logger)
        {
            _hrCoordinatorOrderService = hrCoordinatorOrderService;
            _orderManagementService = orderManagementService;
            _orderRestorationService = orderRestorationService;
            _dashboardService = dashboardService;
            _pathManagementService = pathManagementService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new HRCoordinatorViewModel();

            // Get orders for HR coordinator
            var ordersResult = await _hrCoordinatorOrderService.GetHRCoordinatorOrdersAsync();
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName)
                        ? o.Id.ToString()
                        : $"{o.Id} | {o.EmployeeName}"
                }).ToList();
            }
            else
            {
                TempData["ErrorMessage"] = ordersResult.Message;
            }

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> ProcessOrder(int id)
        {

            var viewModel = new HRCoordinatorViewModel { SelectedOrderId = id };

            // Get order details to verify it exists and is accessible
            var orderResult = await _orderManagementService.GetOrderDetailsAsync(id);
            if (!orderResult.IsSuccess)
            {
                TempData["ErrorMessage"] = "لم يتم العثور على الطلب أو لا يمكن الوصول إليه";
                return RedirectToAction(nameof(Index));
            }

            // Get paths configuration for client-side supervisor mapping
            var pathsConfigResult = await _pathManagementService.GetAllPathsConfigurationAsync();
            if (pathsConfigResult.IsSuccess)
            {
                viewModel.PathsConfiguration = pathsConfigResult.Data;
            }

            // auto-routing information
            var autoRouteResult = await _hrCoordinatorOrderService.GetAutoRoutingInfoAsync(id);
            if (autoRouteResult.IsSuccess)
            {
                viewModel.AutoRoutingInfo = autoRouteResult.Data;
            }
            else
            {
                viewModel.AutoRoutingInfo = new AutoRoutingInfoDto
                {
                    IsAvailable = false,
                    Message = autoRouteResult.Message
                };
            }


            return View(viewModel);

        }

        [HttpGet]
        public async Task<IActionResult> Restore()
        {
            try
            {
                var viewModel = new HRCoordinatorViewModel();

                // Get initial restorable orders
                var restorableOrdersResult = await _orderRestorationService.GetRestorableOrdersAsync("", "today");
                if (restorableOrdersResult.IsSuccess)
                {
                    viewModel.OrderNumbers = restorableOrdersResult.Data.Select(o => new SelectListItem
                    {
                        Value = o.Id.ToString(),
                        Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
                    }).ToList();
                }

                return View("RestoreOrder", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Restore");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل صفحة الاستعادة";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                _logger.LogInformation("Loading HR Coordinator dashboard for user: {Username}", User.Identity?.Name);

                var statisticsResult = await _dashboardService.GetHRCoordinatorStatisticsAsync();

                var viewModel = new HRCoordinatorDashboardViewModel
                {
                    Username = User.Identity?.Name ?? "غير محدد",
                    Role = User.GetUserRoleToDisplay() ?? "غير محدد",
                    Email = User.FindFirstValue("Email") ?? "غير محدد"
                };

                if (statisticsResult.IsSuccess)
                {
                    viewModel.Statistics = statisticsResult.Data ?? new HRCoordinatorStatisticsDto();
                }
                else
                {
                    viewModel.HasError = true;
                    viewModel.ErrorMessage = statisticsResult.Message;
                    _logger.LogWarning("Failed to load HR Coordinator dashboard statistics: {Error}", statisticsResult.Message);
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in HR Coordinator Dashboard");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل لوحة الإحصائيات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SubmitOrder(int orderId, string details, List<string> selectedSupervisors)
        {

            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(details))
            {
                return Json(new { success = false, message = "يرجى إدخال تفاصيل الاعتماد." });
            }

            if (selectedSupervisors == null || !selectedSupervisors.Any())
            {
                return Json(new { success = false, message = "يرجى اختيار المشرفين." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.SubmitOrderByHrCoordinatorAsync(orderId, details, selectedSupervisors, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoRouteOrder(int orderId)
        {

            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.AutoRouteOrderAsync(orderId, userName);

           return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnOrder(int orderId, string returnReason)
        {

            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإعادة." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.ReturnOrderToAssistantManagerAsync(orderId, returnReason, userName);
           
            return Json(new { success = result.IsSuccess, message = result.Message });

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkOrderNeedsAction(int orderId, string actionRequired)
        {
            // return json response for AJAX requests
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(actionRequired))
            {
                return Json(new { success = false, message = "يرجى إدخال تفاصيل الإجراء المطلوب." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.OrderNeedsActionByCoordinatorAsync(orderId, actionRequired, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            // return json response for AJAX requests
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإلغاء." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.RejectOrderByHRCoordinatorAsync(orderId, rejectReason, userName);
           
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            try
            {
                var result = await _orderManagementService.DownloadAttachmentsZipAsync(orderId);
                if (!result.IsSuccess)
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }

                var orderDetailsResult = await _orderManagementService.GetOrderDetailsAsync(orderId);
                if (!orderDetailsResult.IsSuccess)
                {
                    TempData["ErrorMessage"] = "لم يتم العثور على الطلب";
                    return RedirectToAction(nameof(Index));
                }

                var order = orderDetailsResult.Data;
                string zipFileName = CleanFileName($"مرفقات_طلب_{orderId}_{order.EmployeeName}.zip");

                return File(result.Data, "application/zip", zipFileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الملفات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DirectToManager(int orderId, string details)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(details))
            {
                return Json(new { success = false, message = "يرجى إدخال تفاصيل التوجيه." });
            }

            var userName = User.Identity?.Name;
            var result = await _hrCoordinatorOrderService.DirectOrderToManagerAsync(orderId, details, userName);

           return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RestoreOrder(int orderId, string restoreNotes)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(restoreNotes))
            {
                return Json(new { success = false, message = "يرجى إدخال ملاحظات الاستعادة." });
            }

            var userName = User.Identity?.Name;
            var result = await _orderRestorationService.RestoreOrderFromSupervisorsAsync(orderId, restoreNotes, userName);
           
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        public async Task<IActionResult> GetRestorableOrders(string searchTerm, string filter)
        {

            var result = await _orderRestorationService.GetRestorableOrdersAsync(searchTerm, filter);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            var orders = result.Data.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
            }).ToList();


            return Json(new { success = true, data = orders, message = result.Message});
        }

        [HttpPost]
        public async Task<IActionResult> GetRestoreOrderDetails(int orderId)
        {
            var result = await _orderRestorationService.GetRestoreOrderDetailsAsync(orderId);
            
            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });
        }

        private string CleanFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

    }
}
