using Microsoft.AspNetCore.Authentication.Cookies;
using OfficeOpenXml;
using OrderFlowCore.Application;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Data;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Infrastructure;
using OrderFlowCore.Web.Middleware;
using OrderFlowCore.Web.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Register Application and Infrastructure layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddScoped<IEnvironmentService,AppEnvironmentService>();
builder.Services.AddScoped<IFileService, FileService>();
builder.Services.AddScoped<IMenuService, MenuService>();

// Register options from configuration (appsettings.json)
builder.Services.Configure<FileServiceOptions>(builder.Configuration.GetSection(FileServiceOptions.SectionName));
builder.Services.Configure<OrderServiceOptions>(builder.Configuration.GetSection(OrderServiceOptions.SectionName));

// Add Authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/Auth/Login";
        options.LogoutPath = "/Auth/Logout";
        options.AccessDeniedPath = "/Auth/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromHours(2);
    });

// Add Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add Memory Caching
builder.Services.AddMemoryCache();

// Add Response Caching
builder.Services.AddResponseCaching();

ExcelPackage.License.SetNonCommercialOrganization("BredaHospital");

var app = builder.Build();

// Seed database
using (var scope = app.Services.CreateScope())
{
    var seeder = scope.ServiceProvider.GetRequiredService<IDatabaseSeeder>();
    await seeder.SeedAsync();
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

// Add custom exception handling middleware
app.UseExceptionHandling();

// Add performance monitoring middleware
app.UsePerformanceMonitoring();

app.UseHttpsRedirection();
app.UseStaticFiles();

// Add Response Caching middleware
app.UseResponseCaching();

app.UseRouting();

app.UseSession();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
