using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Manager)]
    public class HRManagerController : Controller
    {
        private readonly IHRManagerService _hrManagerService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ILogger<HRManagerController> _logger;

        public HRManagerController(
            IHRManagerService hrManagerService,
            IOrderManagementService orderManagementService,
            ILogger<HRManagerController> logger)
        {
            _hrManagerService = hrManagerService;
            _orderManagementService = orderManagementService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> ProcessOrder()
        {
            var viewModel = new HRManagerProcessOrderViewModel();

            // Get orders for HR manager
            var ordersResult = await _hrManagerService.GetHRManagerOrdersAsync();

            if (!ordersResult.IsSuccess)
            {
                TempData["ErrorMessage"] = ordersResult.Message;
                return View(viewModel);
            }


            viewModel.OrderNumbers = [.. ordersResult.Data!.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = string.IsNullOrEmpty(o.EmployeeName)
                    ? o.Id.ToString()
                    : $"{o.Id} | {o.EmployeeName}"
            })];

            // set auto delete setting
            viewModel.AutoDeleteAttachments = true;

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> ChangeStatus()
        {
            var viewModel = new HRManagerChangeStatusViewModel();

            // Get orders for status change
            var statusChangeOrdersResult = await _hrManagerService.GetOrdersForStatusChangeAsync();

            if (!statusChangeOrdersResult.IsSuccess)
            {
                TempData["ErrorMessage"] = statusChangeOrdersResult.Message;
                return View(viewModel);
            }

            viewModel.StatusChangeOrderNumbers = [.. statusChangeOrdersResult.Data!.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
            })];

            // Get available statuses
            var statusesResult = await _hrManagerService.GetAvailableStatusesAsync();
            viewModel.AvailableStatuses = [.. statusesResult.Data!.Select(s => new SelectListItem
            {
                Value = s.Value,
                Text = s.Text
            })];

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveOrder(int orderId, bool autoDeleteAttachments)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ApproveOrderAsync(orderId, userName, autoDeleteAttachments);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإلغاء" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.RejectOrderAsync(orderId, rejectReason, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnOrder(int orderId, string returnReason)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإعادة" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ReturnOrderAsync(orderId, returnReason, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeOrderStatus(int orderId, string newStatus, string notes)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(newStatus))
            {
                return Json(new { success = false, message = "يرجى اختيار الحالة الجديدة" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ChangeOrderStatusAsync(orderId, newStatus, notes, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });
        }

        [HttpPost]
        public async Task<IActionResult> GetStatusChangeOrders(string searchTerm, string filter)
        {
            var result = await _hrManagerService.GetOrdersForStatusChangeAsync(searchTerm, filter);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            var orders = result.Data.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
            }).ToList();

            return Json(new { success = true, data = orders });

        }

        [HttpPost]
        public async Task<IActionResult> GetStatusChangeOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            return Json(new
            {
                success = true,
                currentStatus = result.Data.OrderStatus,
                orderType = result.Data.OrderType
            });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            try
            {
                var result = await _hrManagerService.DownloadOrderAttachmentsAsync(orderId);
                if (result.IsSuccess)
                {
                    var fileName = $"مرفقات_طلب_{orderId}.zip";
                    return File(result.Data, "application/zip", fileName);
                }

                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(ProcessOrder));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments for order {OrderId}", orderId);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الملفات";
                return RedirectToAction(nameof(ProcessOrder));
            }
        }
    }
}
