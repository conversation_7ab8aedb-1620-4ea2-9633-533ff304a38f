using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Web.Extentions;
using OrderFlowCore.Web.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Supervisor)]
    public class SupervisorOrdersController : Controller
    {
        private readonly ISupervisorOrderService _supervisorOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ISupervisorsFollowUpService _followUpService;

        public SupervisorOrdersController(ISupervisorOrderService supervisorOrderService, IOrderManagementService orderManagementService, ISupervisorsFollowUpService followUpService)
        {
            _supervisorOrderService = supervisorOrderService;
            _orderManagementService = orderManagementService;
            _followUpService = followUpService;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new SupervisorOrdersViewModel { };

            // Load order numbers for dropdown for supervisors

            var supervisorRoleType = User.GetUserRoleType();

            var ordersResult = await _supervisorOrderService.GetSupervisorOrdersAsync(supervisorRoleType);

            if (!ordersResult.IsSuccess)
            {
                TempData["ErrorMessage"] = ordersResult.Message;
                return View(viewModel);
            }

            viewModel.OrderNumbers = ordersResult.Data!.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : $"{o.Id} | {o.EmployeeName}"
            }).ToList();

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });

        }

        [HttpPost]
        public async Task<IActionResult> GetSpecialAction(int orderId)
        {
            var supervisorId = User.Identity?.Name;
            var result = await _orderManagementService.GetOrderSpecialActionAsync(orderId,supervisorId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });

        }

        [HttpGet]
        public async Task<IActionResult> ProcessOrder(int id)
        {
            var viewModel = new SupervisorOrdersViewModel
            {
                SelectedOrderId = id,
            };

            var detailsResult = await _orderManagementService.GetOrderDetailsAsync(id);
            if (!detailsResult.IsSuccess)
            {
                TempData["ErrorMessage"] = detailsResult.Message;
            }

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> FollowUpRecords()
        {
            var viewModel = new SupervisorOrdersViewModel { };

            // Load follow-up records for this supervisor
            var supervisorId = User.Identity?.Name;
            if (!string.IsNullOrEmpty(supervisorId))
            {
                var result = await _followUpService.GetBySupervisorAsync(supervisorId);
                viewModel.FollowUpRecords = result.IsSuccess ? result.Data : new List<SupervisorsFollowUpDto>();
                if (!result.IsSuccess)
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }

            return View("FollowUpRecords", viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SubmitOrder(int orderId)
        {
            if (orderId <= 0)
                return Json(new { success = false, message = "رقم الطلب غير صالح" });

            var supervisorRoleType = User.GetUserRoleType();
            var userName = User.Identity?.Name;
            var result = await _supervisorOrderService.ConfirmOrderBySupervisorAsync(orderId, supervisorRoleType, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkOrderNeedsAction(int orderId, string actionRequired)
        {
            if (orderId <= 0)
                return Json(new { success = false, message = "رقم الطلب غير صالح" });
            if (string.IsNullOrWhiteSpace(actionRequired))
                return Json(new { success = false, message = "يرجى إدخال تفاصيل الإجراء المطلوب" });

            var supervisorRoleType = User.GetUserRoleType();
            var userName = User.Identity?.Name;
            var result = await _supervisorOrderService.NeedsActionBySupervisorAsync(orderId, actionRequired, supervisorRoleType, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnOrder(int orderId, string returnReason)
        {

            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب." });
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإعادة." });
            }

            var userName = User.Identity?.Name;
            var supervisorRoleType = User.GetUserRoleType();

            var result = await _supervisorOrderService.ReturnOrderBySupervisorAsync(orderId, returnReason, supervisorRoleType, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });

        }
        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(orderId);
            if (!result.IsSuccess)
            {
                return RedirectToAction(nameof(Index), new { selectedOrderId = orderId, errorMessage = result.Message });
            }
            var detailsResult = await _orderManagementService.GetOrderDetailsAsync(orderId);
            var fileName = detailsResult.IsSuccess ? $"مرفقات_طلب_{orderId}_{detailsResult.Data.EmployeeName}.zip" : $"مرفقات_طلب_{orderId}.zip";
            return File(result.Data, "application/zip", fileName);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var file = Request.Form.Files["csvFile"];
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "يرجى اختيار ملف CSV صالح" });
            }
            using var stream = file.OpenReadStream();

            var result = await _followUpService.ImportAsync(supervisorId, stream);

            return Json(new { success = result.IsSuccess, message = result.Message });
            
        }

        [HttpGet]
        public async Task<IActionResult> ExportFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var result = await _followUpService.ExportAsync(supervisorId);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true });
            }
            return File(result.Data, "text/csv", "FollowUpRecords.csv");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteAllFollowUpRecords()
        {
            var supervisorId = User.Identity?.Name;
            var result = await _followUpService.DeleteAllAsync(supervisorId);
            if (result.IsSuccess)
            {
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, successMessage = result.Message });
            }
            else
            {
                return RedirectToAction(nameof(Index), new { showRecordsPanel = true, errorMessage = result.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            dto.SupervisorId = User.Identity?.Name;
            var result = await _followUpService.AddAsync(dto);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            dto.SupervisorId = User.Identity?.Name;
            var result = await _followUpService.UpdateAsync(dto);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFollowUpRecordAjax([FromBody] SupervisorsFollowUpDto dto)
        {
            var supervisorId = User.Identity?.Name;
            var result = await _followUpService.DeleteAsync(supervisorId, dto.CivilRecord);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }
    }
}