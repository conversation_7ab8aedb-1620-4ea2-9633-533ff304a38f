using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class StatisticsController : Controller
    {
        private readonly IStatisticsService _statisticsService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(
            IStatisticsService statisticsService,
            ILogger<StatisticsController> logger)
        {
            _statisticsService = statisticsService ?? throw new ArgumentNullException(nameof(statisticsService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new StatisticsViewModel();

                // Get general statistics
                var generalStatsResult = await _statisticsService.GetGeneralStatisticsAsync();
                if (generalStatsResult.IsSuccess)
                {
                    viewModel.GeneralStatistics = generalStatsResult.Data;
                }
                else
                {
                    TempData["ErrorMessage"] = generalStatsResult.Message;
                }

                // Get stage statistics
                var stageStatsResult = await _statisticsService.GetStageStatisticsAsync();
                if (stageStatsResult.IsSuccess)
                {
                    viewModel.StageStatistics = stageStatsResult.Data;
                }
                else
                {
                    TempData["ErrorMessage"] = stageStatsResult.Message;
                }

                // Get department statistics
                var departmentStatsResult = await _statisticsService.GetDepartmentStatisticsAsync();
                if (departmentStatsResult.IsSuccess)
                {
                    viewModel.DepartmentStatistics = departmentStatsResult.Data;
                }
                else
                {
                    TempData["ErrorMessage"] = departmentStatsResult.Message;
                }

                // Get supervisor statistics
                var supervisorStatsResult = await _statisticsService.GetSupervisorStatisticsAsync();
                if (supervisorStatsResult.IsSuccess)
                {
                    viewModel.SupervisorStatistics = supervisorStatsResult.Data;
                }
                else
                {
                    TempData["ErrorMessage"] = supervisorStatsResult.Message;
                }

                // Get transfer type statistics
                var transferTypeStatsResult = await _statisticsService.GetTransferTypeStatisticsAsync();
                if (transferTypeStatsResult.IsSuccess)
                {
                    viewModel.TransferTypeStatistics = transferTypeStatsResult.Data;
                }
                else
                {
                    TempData["ErrorMessage"] = transferTypeStatsResult.Message;
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading statistics");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الإحصائيات";
                return View(new StatisticsViewModel());
            }
        }
    }
} 