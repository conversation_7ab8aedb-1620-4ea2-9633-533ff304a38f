@model OrderFlowCore.Web.ViewModels.StatisticsViewModel
@{
    ViewData["Title"] = "الإحصائيات";
}

<div class="container-fluid mt-4">
    <!-- Page Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-chart-bar ml-2"></i> الإحصائيات</h2>
                <p>إحصائيات شاملة لجميع الطلبات والمراحل</p>
            </div>
        </div>
    </div>

    <!-- Messages -->
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle ml-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- العمود الأول: إحصائيات عامة -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="card-title mb-0">إحصائيات عامة</h3>
        </div>
        <div class="card-body">
            <!-- إحصائيات إجمالية -->
            <div class="summary-stats mb-4">
                <div class="row g-3">
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">إجمالي الطلبات</div>
                            <div class="stat-value text-primary">
                                @Model.GeneralStatistics.TotalRequests
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">الطلبات المنجزة</div>
                            <div class="stat-value text-success">
                                @Model.GeneralStatistics.CompletedRequests
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">تحت التنفيذ</div>
                            <div class="stat-value text-warning">
                                @Model.GeneralStatistics.PendingRequests
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">متوسط زمن الإنجاز</div>
                            <div class="stat-value text-info">
                                @Model.GeneralStatistics.AverageTimeToComplete أيام
                            </div>
                        </div>
                    </div>
                </div>
            </div>
     
            <!-- إحصائيات المراحل -->
            <div class="stages-stats">
                <h4 class="mb-3">توزيع الطلبات حسب المراحل</h4>
                <div class="table-responsive">
                    <table class="table table-hover border">
                        <tbody>
                            <tr>
                                <td><strong>مدير القسم</strong></td>
                                <td class="stage-value">@Model.StageStatistics.DepartmentManager</td>
                            </tr>
                            <tr>
                                <td><strong>مساعد المدير للخدمات الطبية</strong></td>
                                <td class="stage-value">@Model.StageStatistics.AssistantManagerA1</td>
                            </tr>
                            <tr>
                                <td><strong>مساعد المدير لخدمات التمريض</strong></td>
                                <td class="stage-value">@Model.StageStatistics.AssistantManagerA2</td>
                            </tr>
                            <tr>
                                <td><strong>مساعد المدير للخدمات الإدارية</strong></td>
                                <td class="stage-value">@Model.StageStatistics.AssistantManagerA3</td>
                            </tr>
                            <tr>
                                <td><strong>مساعد المدير للموارد البشرية</strong></td>
                                <td class="stage-value">@Model.StageStatistics.AssistantManagerA4</td>
                            </tr>
                            <tr>
                                <td><strong>منسق الموارد البشرية</strong></td>
                                <td class="stage-value">@Model.StageStatistics.HRCoordinator</td>
                            </tr>
                            <tr>
                                <td><strong>المشرفين</strong></td>
                                <td class="stage-value">@Model.StageStatistics.Supervisors</td>
                            </tr>
                            <tr>
                                <td><strong>مدير الموارد البشرية</strong></td>
                               <td class="stage-value">@Model.StageStatistics.HRManager</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
     </div>
     
     
     <div class="card">
         <div class="card-header bg-light">
             <ul class="nav nav-tabs card-header-tabs" role="tablist">
                 <li class="nav-item">
                     <a class="nav-link active" data-bs-toggle="tab" href="#departmentStats">
                         <i class="fas fa-building ml-1"></i>
                         إحصائيات الأقسام
                     </a>
                 </li>
                 <li class="nav-item">
                     <a class="nav-link" data-bs-toggle="tab" href="#supervisors">
                         <i class="fas fa-users-cog ml-1"></i>
                         إحصائيات المشرفين
                     </a>
                 </li>
                 <li class="nav-item">
                     <a class="nav-link" data-bs-toggle="tab" href="#transferTypes">
                         <i class="fas fa-exchange-alt ml-1"></i>
                         نوع التحويل
                     </a>
                 </li>
             </ul>
         </div>
     
         <div class="card-body">
             <div class="tab-content"> 
                 <!-- تبويب إحصائيات الأقسام -->
                 <div id="departmentStats" class="tab-pane fade show active">
                     <div class="card h-100">
                         <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                             <h3 class="card-title mb-0">إحصائيات الأقسام التفصيلية</h3>
                         </div>
                         <div class="card-body">
                             <div class="summary-stats bg-light p-3 rounded mb-4">
                                 <div class="summary-stats">
                                     <strong class="completed">إجمالي الأقسام: @Model.DepartmentStatistics.Count</strong> | 
                                     <strong class="avg-completion-time">إجمالي الطلبات: @Model.DepartmentStatistics.Sum(d => d.TotalOrders)</strong>
                                 </div>
                             </div>
                             <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                                 @foreach (var dept in Model.DepartmentStatistics)
                                 {
                                     <div class='department-row'>
                                         <strong>@dept.DepartmentName:</strong>
                                         <span class='text-primary'>إجمالي: @dept.TotalOrders</span> | 
                                         <span class='text-success'>منجز: @dept.CompletedOrders</span> | 
                                         <span class='text-warning'>تحت التنفيذ: @dept.PendingOrders</span> | 
                                         <span class='text-danger'>ملغي: @dept.CancelledOrders</span>
                                     </div>
                                 }
                             </div>
                         </div>
                     </div>
                 </div>
     
                 <!-- تبويب إحصائيات المشرفين -->
                 <div id="supervisors" class="tab-pane fade">
                     <div class="card h-100">
                         <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                             <h3 class="card-title mb-0">إحصائيات المشرفين التفصيلية</h3>
                         </div>
                         <div class="card-body">
                             <div class="summary-stats bg-light p-3 rounded mb-4">
                                 <div class='summary-stats'>
                                     <strong class='under-execution'>تحت التنفيذ: @Model.SupervisorStatistics.Sum(s => s.UnderExecution)</strong> | 
                                     <strong class='completed'>منجز: @Model.SupervisorStatistics.Sum(s => s.Completed)</strong> | 
                                     <strong class='text-warning'>يتطلب إجراءات: @Model.SupervisorStatistics.Sum(s => s.NeedsAction)</strong> | 
                                     <strong class='text-danger'>معاد: @Model.SupervisorStatistics.Sum(s => s.Returned)</strong> |
                                     <strong class='avg-completion-time'>متوسط زمن الإنجاز: @(Model.SupervisorStatistics.Any() ? Model.SupervisorStatistics.Average(s => s.AverageCompletionTime).ToString("F2") : "0") أيام</strong>
                                 </div>
                             </div>
                             <div class="supervisor-list" style="max-height: 400px; overflow-y: auto;">
                                 @foreach (var supervisor in Model.SupervisorStatistics)
                                 {
                                     <div class='department-row'>
                                         <strong>@supervisor.SupervisorName:</strong>
                                         <span class='text-warning'>تحت التنفيذ: @supervisor.UnderExecution</span> | 
                                         <span class='text-success'>منجز: @supervisor.Completed</span> | 
                                         <span class='text-warning'>يتطلب إجراءات: @supervisor.NeedsAction</span> | 
                                         <span class='text-danger'>معاد: @supervisor.Returned</span> | 
                                         <span class='avg-completion-time'>متوسط زمن الإنجاز: @supervisor.AverageCompletionTime.ToString("F2") أيام</span>
                                     </div>
                                 }
                             </div>
                         </div>
                     </div>
                 </div>
     
                 <!-- تبويب نوع التحويل -->
                 <div id="transferTypes" class="tab-pane fade">
                     <div class="card h-100">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                             <h3 class="card-title mb-0">إحصائيات نوع التحويل</h3>
                         </div>
                         <div class="card-body">
                             <div class="summary-stats bg-light p-3 rounded mb-4">
                                 <div class='summary-stats'>
                                     <strong class='completed'>إجمالي التحويلات: @Model.TransferTypeStatistics.Sum(t => t.Count)</strong> | 
                                     <strong class='avg-completion-time'>عدد الأنواع: @Model.TransferTypeStatistics.Count</strong>
                                 </div>
                             </div>
                             <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                                 @foreach (var transferType in Model.TransferTypeStatistics)
                                 {
                                     <div class='department-row'>
                                         <strong>@transferType.TransferType:</strong>
                                         <span class='text-primary'>العدد: @transferType.Count</span>
                                     </div>
                                 }
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div>
</div>

<style>
.stat-box {
    background: #f8f9fa;
    transition: transform 0.2s;
}

.stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.department-row {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
}

.department-row:last-child {
    border-bottom: none;
}

.stage-value {
    font-weight: bold;
    color: #007bff;
}

.summary-stats {
    font-size: 0.9rem;
}

.completed {
    color: #28a745;
}

.under-execution {
    color: #ffc107;
}

.avg-completion-time {
    color: #17a2b8;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-primary {
    color: #007bff !important;
}

.text-info {
    color: #17a2b8 !important;
}
</style> 